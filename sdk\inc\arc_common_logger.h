#pragma once

#include <memory>
#include <string>
#include <vector>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/async.h>

namespace arc {

/**
 * @brief Configuration structure for the logger
 */
struct LoggerConfig {
    std::string name = "arc_logger";                    // Logger name
    std::string log_file_path = "logs/app.log";         // Log file path
    size_t max_file_size = 1024 * 1024 * 10;           // 10MB default
    size_t max_files = 5;                               // Keep 5 rotated files
    spdlog::level::level_enum log_level = spdlog::level::info;  // Default log level
    bool enable_async = false;                          // Async logging disabled by default
    size_t async_queue_size = 8192;                     // Async queue size
    size_t async_thread_count = 1;                      // Number of async threads
    std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%n] [%l] [%t] %v";  // Log pattern
    bool enable_console = true;                         // Also log to console
    bool flush_on_error = true;                         // Auto flush on error level
};

/**
 * @brief Arc Common Logger - A wrapper around spdlog with rotating file sink
 *
 * This class provides a simplified interface to spdlog with built-in support for
 * rotating file sinks and optional async logging. It supports multiple instances
 * with different configurations.
 */
class Logger {
public:
    /**
     * @brief Construct a new Logger object with default configuration
     */
    Logger();

    /**
     * @brief Construct a new Logger object with custom configuration
     * @param config Logger configuration
     */
    explicit Logger(const LoggerConfig& config);

    /**
     * @brief Destroy the Logger object
     */
    ~Logger();

    // Disable copy constructor and assignment operator
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    // Enable move constructor and assignment operator
    Logger(Logger&&) = default;
    Logger& operator=(Logger&&) = default;

    /**
     * @brief Initialize the logger with the given configuration
     * @param config Logger configuration
     * @return true if initialization successful, false otherwise
     */
    bool initialize(const LoggerConfig& config);

    /**
     * @brief Check if the logger is initialized and ready to use
     * @return true if initialized, false otherwise
     */
    bool is_initialized() const;

    /**
     * @brief Get the current logger configuration
     * @return const LoggerConfig& Current configuration
     */
    const LoggerConfig& get_config() const;

    /**
     * @brief Set the log level
     * @param level New log level
     */
    void set_level(spdlog::level::level_enum level);

    /**
     * @brief Get the current log level
     * @return spdlog::level::level_enum Current log level
     */
    spdlog::level::level_enum get_level() const;

    /**
     * @brief Set the log pattern
     * @param pattern New log pattern
     */
    void set_pattern(const std::string& pattern);

    /**
     * @brief Flush all sinks
     */
    void flush();

    /**
     * @brief Enable or disable automatic flushing on error level
     * @param enable True to enable, false to disable
     */
    void set_flush_on_error(bool enable);

    // Logging methods
    template<typename... Args>
    void trace(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->trace(fmt, std::forward<Args>(args)...);
        }
    }

    template<typename... Args>
    void debug(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->debug(fmt, std::forward<Args>(args)...);
        }
    }

    template<typename... Args>
    void info(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->info(fmt, std::forward<Args>(args)...);
        }
    }

    template<typename... Args>
    void warn(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->warn(fmt, std::forward<Args>(args)...);
        }
    }

    template<typename... Args>
    void error(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->error(fmt, std::forward<Args>(args)...);
            if (config_.flush_on_error) {
                flush();
            }
        }
    }

    template<typename... Args>
    void critical(const std::string& fmt, Args&&... args) {
        if (logger_) {
            logger_->critical(fmt, std::forward<Args>(args)...);
            if (config_.flush_on_error) {
                flush();
            }
        }
    }

    /**
     * @brief Get the underlying spdlog logger (for advanced usage)
     * @return std::shared_ptr<spdlog::logger> Underlying logger
     */
    std::shared_ptr<spdlog::logger> get_spdlog_logger() const;

    /**
     * @brief Create a directory if it doesn't exist
     * @param path Directory path
     * @return true if directory exists or was created successfully
     */
    static bool create_directory(const std::string& path);

    /**
     * @brief Get a list of all registered logger names
     * @return std::vector<std::string> List of logger names
     */
    static std::vector<std::string> get_all_logger_names();

    /**
     * @brief Shutdown all loggers and cleanup resources
     */
    static void shutdown_all();

private:
    LoggerConfig config_;
    std::shared_ptr<spdlog::logger> logger_;
    bool initialized_;

    /**
     * @brief Create the rotating file sink
     * @return std::shared_ptr<spdlog::sinks::sink> Created sink
     */
    std::shared_ptr<spdlog::sinks::sink> create_rotating_sink();

    /**
     * @brief Create the console sink if enabled
     * @return std::shared_ptr<spdlog::sinks::sink> Created sink or nullptr
     */
    std::shared_ptr<spdlog::sinks::sink> create_console_sink();

    /**
     * @brief Setup async logging if enabled
     */
    void setup_async_logging();

    /**
     * @brief Extract directory path from file path
     * @param file_path Full file path
     * @return std::string Directory path
     */
    static std::string extract_directory(const std::string& file_path);
};

} // namespace arc