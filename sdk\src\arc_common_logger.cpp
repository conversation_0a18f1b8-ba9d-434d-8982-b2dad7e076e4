#include "arc_common_logger.h"
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/async.h>
#include <filesystem>
#include <iostream>

namespace arc {

// Constructor with default configuration
Logger::Logger() : initialized_(false) {
    LoggerConfig default_config;
    initialize(default_config);
}

// Constructor with custom configuration
Logger::Logger(const LoggerConfig& config) : initialized_(false) {
    initialize(config);
}

// Destructor
Logger::~Logger() {
    if (logger_) {
        logger_->flush();
    }
}

bool Logger::initialize(const LoggerConfig& config) {
    try {
        config_ = config;

        // Create directory for log file if it doesn't exist
        std::string log_dir = extract_directory(config_.log_file_path);
        if (!log_dir.empty() && !create_directory(log_dir)) {
            std::cerr << "Failed to create log directory: " << log_dir << std::endl;
            return false;
        }

        // Setup async logging if enabled
        if (config_.enable_async) {
            setup_async_logging();
        }

        // Create sinks
        std::vector<spdlog::sink_ptr> sinks;

        // Add rotating file sink
        auto file_sink = create_rotating_sink();
        if (file_sink) {
            sinks.push_back(file_sink);
        }

        // Add console sink if enabled
        if (config_.enable_console) {
            auto console_sink = create_console_sink();
            if (console_sink) {
                sinks.push_back(console_sink);
            }
        }

        if (sinks.empty()) {
            std::cerr << "No sinks available for logger" << std::endl;
            return false;
        }

        // Create logger
        if (config_.enable_async) {
            logger_ = std::make_shared<spdlog::async_logger>(
                config_.name,
                sinks.begin(),
                sinks.end(),
                spdlog::thread_pool(),
                spdlog::async_overflow_policy::block
            );
        } else {
            logger_ = std::make_shared<spdlog::logger>(config_.name, sinks.begin(), sinks.end());
        }

        // Configure logger
        logger_->set_level(config_.log_level);
        logger_->set_pattern(config_.pattern);

        if (config_.flush_on_error) {
            logger_->flush_on(spdlog::level::err);
        }

        // Register logger with spdlog
        spdlog::register_logger(logger_);

        initialized_ = true;
        return true;

    } catch (const std::exception& ex) {
        std::cerr << "Logger initialization failed: " << ex.what() << std::endl;
        initialized_ = false;
        return false;
    }
}

bool Logger::is_initialized() const {
    return initialized_ && logger_ != nullptr;
}

const LoggerConfig& Logger::get_config() const {
    return config_;
}

void Logger::set_level(spdlog::level::level_enum level) {
    if (logger_) {
        logger_->set_level(level);
        config_.log_level = level;
    }
}

spdlog::level::level_enum Logger::get_level() const {
    if (logger_) {
        return logger_->level();
    }
    return spdlog::level::off;
}

void Logger::set_pattern(const std::string& pattern) {
    if (logger_) {
        logger_->set_pattern(pattern);
        config_.pattern = pattern;
    }
}

void Logger::flush() {
    if (logger_) {
        logger_->flush();
    }
}

void Logger::set_flush_on_error(bool enable) {
    config_.flush_on_error = enable;
    if (logger_) {
        if (enable) {
            logger_->flush_on(spdlog::level::err);
        } else {
            logger_->flush_on(spdlog::level::off);
        }
    }
}

std::shared_ptr<spdlog::logger> Logger::get_spdlog_logger() const {
    return logger_;
}

// Static methods
bool Logger::create_directory(const std::string& path) {
    try {
        if (path.empty()) {
            return true;
        }

        std::filesystem::path dir_path(path);
        if (std::filesystem::exists(dir_path)) {
            return std::filesystem::is_directory(dir_path);
        }

        return std::filesystem::create_directories(dir_path);
    } catch (const std::exception& ex) {
        std::cerr << "Failed to create directory '" << path << "': " << ex.what() << std::endl;
        return false;
    }
}

std::vector<std::string> Logger::get_all_logger_names() {
    std::vector<std::string> names;
    spdlog::apply_all([&names](std::shared_ptr<spdlog::logger> logger) {
        names.push_back(logger->name());
    });
    return names;
}

void Logger::shutdown_all() {
    spdlog::shutdown();
}

// Private methods
std::shared_ptr<spdlog::sinks::sink> Logger::create_rotating_sink() {
    try {
        auto sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            config_.log_file_path,
            config_.max_file_size,
            config_.max_files
        );
        return sink;
    } catch (const std::exception& ex) {
        std::cerr << "Failed to create rotating file sink: " << ex.what() << std::endl;
        return nullptr;
    }
}

std::shared_ptr<spdlog::sinks::sink> Logger::create_console_sink() {
    try {
        auto sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        return sink;
    } catch (const std::exception& ex) {
        std::cerr << "Failed to create console sink: " << ex.what() << std::endl;
        return nullptr;
    }
}

void Logger::setup_async_logging() {
    try {
        // Initialize async logging with specified queue size and thread count
        spdlog::init_thread_pool(config_.async_queue_size, config_.async_thread_count);
    } catch (const std::exception& ex) {
        std::cerr << "Failed to setup async logging: " << ex.what() << std::endl;
        // Fall back to synchronous logging
        config_.enable_async = false;
    }
}

std::string Logger::extract_directory(const std::string& file_path) {
    try {
        std::filesystem::path path(file_path);
        return path.parent_path().string();
    } catch (const std::exception& ex) {
        std::cerr << "Failed to extract directory from path '" << file_path << "': " << ex.what() << std::endl;
        return "";
    }
}

} // namespace arc